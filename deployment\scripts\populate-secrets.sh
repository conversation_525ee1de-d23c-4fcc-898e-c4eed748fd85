#!/bin/bash

# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

set -e

PROJECT_ID=$1
ENVIRONMENT=$2

if [ -z "$PROJECT_ID" ] || [ -z "$ENVIRONMENT" ]; then
    echo "Usage: $0 <project-id> <environment>"
    echo "Example: $0 my-staging-project staging"
    echo "         $0 my-production-project production"
    exit 1
fi

echo "🔐 Populating secrets for $ENVIRONMENT environment in project $PROJECT_ID"
echo ""

# Function to create or update secret
create_or_update_secret() {
    local secret_name=$1
    local secret_value=$2
    
    echo "Setting secret: $secret_name"
    
    # Check if secret exists and has versions
    if gcloud secrets describe "$secret_name" --project="$PROJECT_ID" >/dev/null 2>&1; then
        echo "  Secret $secret_name already exists, adding new version..."
    else
        echo "  Error: Secret $secret_name does not exist. Please run terraform apply first."
        return 1
    fi
    
    # Add new secret version
    echo -n "$secret_value" | gcloud secrets versions add "$secret_name" \
        --project="$PROJECT_ID" \
        --data-file=- >/dev/null 2>&1
    
    if [ $? -eq 0 ]; then
        echo "  ✅ Successfully updated $secret_name"
    else
        echo "  ❌ Failed to update $secret_name"
        return 1
    fi
}

# Function to validate input
validate_input() {
    local input=$1
    local field_name=$2
    
    if [ -z "$input" ]; then
        echo "❌ Error: $field_name cannot be empty"
        return 1
    fi
    return 0
}

# Function to validate URL format
validate_url() {
    local url=$1
    if [[ ! "$url" =~ ^https?:// ]]; then
        echo "❌ Error: URL must start with http:// or https://"
        return 1
    fi
    return 0
}

# Function to validate phone number format
validate_phone() {
    local phone=$1
    if [[ ! "$phone" =~ ^\+[1-9][0-9]{1,14}$ ]]; then
        echo "❌ Error: Phone number must be in international format (e.g., +**********)"
        return 1
    fi
    return 0
}

echo "📱 Twilio Configuration:"
echo "Please provide your Twilio credentials for the $ENVIRONMENT environment."
echo ""

# Twilio Account SID
while true; do
    read -p "Enter Twilio Account SID: " TWILIO_ACCOUNT_SID
    if validate_input "$TWILIO_ACCOUNT_SID" "Twilio Account SID"; then
        break
    fi
done

# Twilio Auth Token
while true; do
    read -s -p "Enter Twilio Auth Token: " TWILIO_AUTH_TOKEN
    echo
    if validate_input "$TWILIO_AUTH_TOKEN" "Twilio Auth Token"; then
        break
    fi
done

# Twilio WhatsApp Number
while true; do
    read -p "Enter Twilio WhatsApp Number (format: +**********): " TWILIO_WHATSAPP_NUMBER
    if validate_input "$TWILIO_WHATSAPP_NUMBER" "Twilio WhatsApp Number" && validate_phone "$TWILIO_WHATSAPP_NUMBER"; then
        break
    fi
done

echo ""
echo "🏢 Odoo Configuration:"
echo "Please provide your Odoo API credentials for the $ENVIRONMENT environment."
echo ""

# Odoo API Base URL
while true; do
    read -p "Enter Odoo API Base URL (e.g., https://your-instance.odoo.com): " ODOO_API_BASE_URL
    if validate_input "$ODOO_API_BASE_URL" "Odoo API Base URL" && validate_url "$ODOO_API_BASE_URL"; then
        break
    fi
done

# Odoo API Key
while true; do
    read -s -p "Enter Odoo API Key: " ODOO_API_KEY
    echo
    if validate_input "$ODOO_API_KEY" "Odoo API Key"; then
        break
    fi
done

echo ""
echo "🔄 Creating/updating secrets..."
echo ""

# Create/update secrets
create_or_update_secret "my-agent-twilio-account-sid" "$TWILIO_ACCOUNT_SID"
create_or_update_secret "my-agent-twilio-auth-token" "$TWILIO_AUTH_TOKEN"
create_or_update_secret "my-agent-twilio-whatsapp-number" "$TWILIO_WHATSAPP_NUMBER"
create_or_update_secret "my-agent-odoo-api-base-url" "$ODOO_API_BASE_URL"
create_or_update_secret "my-agent-odoo-api-key" "$ODOO_API_KEY"

echo ""
echo "✅ All secrets populated successfully for $ENVIRONMENT environment!"
echo ""
echo "📋 Summary of secrets created/updated:"
echo "  - my-agent-twilio-account-sid"
echo "  - my-agent-twilio-auth-token"
echo "  - my-agent-twilio-whatsapp-number"
echo "  - my-agent-odoo-api-base-url"
echo "  - my-agent-odoo-api-key"
echo ""
echo "Note: Database password (my-agent-db-password) is automatically managed by Terraform."
echo ""
echo "🔍 To verify secrets were created, run:"
echo "  gcloud secrets list --project=$PROJECT_ID"
echo ""
echo "🔐 To test secret access, run:"
echo "  gcloud secrets versions access latest --secret=my-agent-twilio-account-sid --project=$PROJECT_ID"
