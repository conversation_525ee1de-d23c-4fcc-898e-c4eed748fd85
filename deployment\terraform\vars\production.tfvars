# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# =============================================================================
# PRODUCTION ENVIRONMENT CONFIGURATION
# =============================================================================
# This file contains production-specific variable values for Terraform deployment.
# Update the project IDs below with your actual Google Cloud project IDs.

# Project name used for resource naming
project_name = "my-agent"

# =============================================================================
# GOOGLE CLOUD PROJECT IDS
# =============================================================================
# TODO: Replace these placeholder values with your actual Google Cloud project IDs

# Your Production Google Cloud project id
prod_project_id = "my-agent-prod-replace-me"

# Your Staging / Test Google Cloud project id
staging_project_id = "my-agent-staging-replace-me"

# Your Google Cloud project ID that will be used to host the Cloud Build pipelines
cicd_runner_project_id = "my-agent-cicd-replace-me"

# =============================================================================
# CLOUD BUILD CONFIGURATION
# =============================================================================

# Name of the host connection you created in Cloud Build
host_connection_name = "git-my-agent"

# Name of the repository you added to Cloud Build
repository_name = "repo-my-agent"

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# The Google Cloud region you will use to deploy the infrastructure
region = "us-central1"

# =============================================================================
# LOGGING AND MONITORING CONFIGURATION
# =============================================================================

# Filter for telemetry logs in BigQuery
telemetry_logs_filter = "jsonPayload.attributes.\"traceloop.association.properties.log_type\"=\"tracing\" jsonPayload.resource.attributes.\"service.name\"=\"my-agent\""

# Filter for feedback logs in BigQuery
feedback_logs_filter = "jsonPayload.log_type=\"feedback\""

# =============================================================================
# PRODUCTION-SPECIFIC DATABASE CONFIGURATION
# =============================================================================
# These values override the defaults for production environment

# Database tier for production (high performance)
database_tier_prod = "db-custom-2-4096"

# Database availability for production (regional for high availability)
database_availability_prod = "REGIONAL"

# Database disk size for production (larger for data growth)
database_disk_size_prod = 100

# Database disk auto-resize limit for production
database_disk_limit_prod = 500

# Database backup retention for production (longer retention)
database_backup_retention_prod = 30

# Database max connections for production (higher limit)
database_max_connections_prod = "100"
