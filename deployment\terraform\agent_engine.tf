# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Agent Engine staging buckets
resource "google_storage_bucket" "agent_engine_staging" {
  for_each                    = local.deploy_project_ids
  name                        = "${each.value}-agent-engine"
  location                    = var.region
  project                     = each.value
  uniform_bucket_level_access = true
  force_destroy               = false

  versioning {
    enabled = true
  }

  lifecycle_rule {
    condition {
      age = 30
    }
    action {
      type = "Delete"
    }
  }

  lifecycle_rule {
    condition {
      num_newer_versions = 5
    }
    action {
      type = "Delete"
    }
  }

  depends_on = [google_project_service.shared_services]
}

# Service account for Agent Engine
resource "google_service_account" "agent_engine_sa" {
  for_each     = local.deploy_project_ids
  project      = each.value
  account_id   = "${var.project_name}-agent-engine"
  display_name = "Agent Engine Service Account - ${title(each.key)}"
  description  = "Service account for Agent Engine deployment in ${each.key} environment"
}

# IAM bindings for Agent Engine service accounts - Project level roles
resource "google_project_iam_member" "agent_engine_project_permissions" {
  for_each = {
    for pair in setproduct(keys(local.deploy_project_ids), var.agentengine_sa_roles) : 
    "${pair[0]}_${replace(pair[1], ".", "_")}" => {
      project = local.deploy_project_ids[pair[0]]
      role    = pair[1]
      env     = pair[0]
    }
  }

  project = each.value.project
  role    = each.value.role
  member  = "serviceAccount:${google_service_account.agent_engine_sa[each.value.env].email}"
}

# IAM bindings for Agent Engine staging buckets
resource "google_storage_bucket_iam_member" "agent_engine_bucket_access" {
  for_each = local.deploy_project_ids
  bucket   = google_storage_bucket.agent_engine_staging[each.key].name
  role     = "roles/storage.objectAdmin"
  member   = "serviceAccount:${google_service_account.agent_engine_sa[each.key].email}"
}

# IAM bindings for CI/CD service account to access Agent Engine buckets
resource "google_storage_bucket_iam_member" "cicd_agent_engine_bucket_access" {
  for_each = local.deploy_project_ids
  bucket   = google_storage_bucket.agent_engine_staging[each.key].name
  role     = "roles/storage.objectAdmin"
  member   = "serviceAccount:${var.project_name}-cb@${var.cicd_runner_project_id}.iam.gserviceaccount.com"
}

# Additional IAM permissions for Agent Engine service accounts to impersonate CI/CD SA
resource "google_service_account_iam_member" "agent_engine_impersonate_cicd" {
  for_each           = local.deploy_project_ids
  service_account_id = "projects/${var.cicd_runner_project_id}/serviceAccounts/${var.project_name}-cb@${var.cicd_runner_project_id}.iam.gserviceaccount.com"
  role               = "roles/iam.serviceAccountTokenCreator"
  member             = "serviceAccount:${google_service_account.agent_engine_sa[each.key].email}"
}

# Output Agent Engine service account emails for reference
output "agent_engine_service_accounts" {
  description = "Agent Engine service account emails by environment"
  value = {
    for env, project_id in local.deploy_project_ids :
    env => google_service_account.agent_engine_sa[env].email
  }
}

# Output Agent Engine staging bucket names for reference
output "agent_engine_staging_buckets" {
  description = "Agent Engine staging bucket names by environment"
  value = {
    for env, project_id in local.deploy_project_ids :
    env => google_storage_bucket.agent_engine_staging[env].name
  }
}
