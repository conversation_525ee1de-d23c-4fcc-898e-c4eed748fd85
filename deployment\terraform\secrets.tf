# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Database password secrets (auto-generated from Task 1.1)
resource "google_secret_manager_secret" "db_password" {
  for_each  = local.deploy_project_ids
  project   = each.value
  secret_id = "my-agent-db-password"

  replication {
    auto {}
  }
  
  depends_on = [google_project_service.shared_services]
}

resource "google_secret_manager_secret_version" "db_password" {
  for_each    = local.deploy_project_ids
  secret      = google_secret_manager_secret.db_password[each.key].id
  secret_data = random_password.db_password[each.key].result
}

# Twilio secrets (manual population required)
resource "google_secret_manager_secret" "twilio_account_sid" {
  for_each  = local.deploy_project_ids
  project   = each.value
  secret_id = "my-agent-twilio-account-sid"

  replication {
    auto {}
  }
  
  depends_on = [google_project_service.shared_services]
}

resource "google_secret_manager_secret" "twilio_auth_token" {
  for_each  = local.deploy_project_ids
  project   = each.value
  secret_id = "my-agent-twilio-auth-token"

  replication {
    auto {}
  }
  
  depends_on = [google_project_service.shared_services]
}

resource "google_secret_manager_secret" "twilio_whatsapp_number" {
  for_each  = local.deploy_project_ids
  project   = each.value
  secret_id = "my-agent-twilio-whatsapp-number"

  replication {
    auto {}
  }
  
  depends_on = [google_project_service.shared_services]
}

# Odoo secrets (manual population required)
resource "google_secret_manager_secret" "odoo_api_base_url" {
  for_each  = local.deploy_project_ids
  project   = each.value
  secret_id = "my-agent-odoo-api-base-url"

  replication {
    auto {}
  }
  
  depends_on = [google_project_service.shared_services]
}

resource "google_secret_manager_secret" "odoo_api_key" {
  for_each  = local.deploy_project_ids
  project   = each.value
  secret_id = "my-agent-odoo-api-key"

  replication {
    auto {}
  }
  
  depends_on = [google_project_service.shared_services]
}

# IAM permissions for Agent Engine service accounts to access secrets
resource "google_secret_manager_secret_iam_member" "agent_engine_secret_access" {
  for_each = {
    for pair in setproduct(keys(local.deploy_project_ids), [
      "my-agent-db-password",
      "my-agent-twilio-account-sid", 
      "my-agent-twilio-auth-token",
      "my-agent-twilio-whatsapp-number",
      "my-agent-odoo-api-base-url",
      "my-agent-odoo-api-key"
    ]) : "${pair[0]}_${pair[1]}" => {
      project   = local.deploy_project_ids[pair[0]]
      secret_id = pair[1]
      env       = pair[0]
    }
  }

  project   = each.value.project
  secret_id = each.value.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.project_name}-agent-engine@${each.value.project}.iam.gserviceaccount.com"
  
  depends_on = [
    google_secret_manager_secret.db_password,
    google_secret_manager_secret.twilio_account_sid,
    google_secret_manager_secret.twilio_auth_token,
    google_secret_manager_secret.twilio_whatsapp_number,
    google_secret_manager_secret.odoo_api_base_url,
    google_secret_manager_secret.odoo_api_key
  ]
}

# IAM permissions for CI/CD service account to access secrets
resource "google_secret_manager_secret_iam_member" "cicd_secret_access" {
  for_each = {
    for pair in setproduct(keys(local.deploy_project_ids), [
      "my-agent-db-password",
      "my-agent-twilio-account-sid",
      "my-agent-twilio-auth-token", 
      "my-agent-twilio-whatsapp-number",
      "my-agent-odoo-api-base-url",
      "my-agent-odoo-api-key"
    ]) : "${pair[0]}_${pair[1]}" => {
      project   = local.deploy_project_ids[pair[0]]
      secret_id = pair[1]
      env       = pair[0]
    }
  }

  project   = each.value.project
  secret_id = each.value.secret_id
  role      = "roles/secretmanager.secretAccessor"
  member    = "serviceAccount:${var.project_name}-cb@${var.cicd_runner_project_id}.iam.gserviceaccount.com"
  
  depends_on = [
    google_secret_manager_secret.db_password,
    google_secret_manager_secret.twilio_account_sid,
    google_secret_manager_secret.twilio_auth_token,
    google_secret_manager_secret.twilio_whatsapp_number,
    google_secret_manager_secret.odoo_api_base_url,
    google_secret_manager_secret.odoo_api_key
  ]
}
