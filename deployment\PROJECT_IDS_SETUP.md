# Project IDs Configuration Guide

This guide explains how to configure your Google Cloud project IDs for deployment.

## 📋 Required Project IDs

You need **three Google Cloud projects** for a complete deployment:

### 1. **CI/CD Project** (`my-agent-cicd-replace-me`)
- **Purpose**: Hosts Cloud Build pipelines and CI/CD infrastructure
- **Resources**: Cloud Build triggers, Artifact Registry, load test results storage
- **Required APIs**: Cloud Build, Artifact Registry, Cloud Resource Manager

### 2. **Staging Project** (`my-agent-staging-replace-me`)
- **Purpose**: Staging environment for testing deployments
- **Resources**: Cloud SQL (db-f1-micro), Secret Manager, Agent Engine, BigQuery
- **Required APIs**: All APIs from Tasks 1.1-1.3 (SQL Admin, Secret Manager, AI Platform, etc.)

### 3. **Production Project** (`my-agent-prod-replace-me`)
- **Purpose**: Production environment for live deployments
- **Resources**: Cloud SQL (db-custom-2-4096), Secret Manager, Agent Engine, BigQuery
- **Required APIs**: All APIs from Tasks 1.1-1.3 (SQL Admin, Secret Manager, AI Platform, etc.)

## 🔧 How to Update Project IDs

### Option 1: Update Environment-Specific Files (Recommended)

1. **For Staging Deployment**:
   ```bash
   # Edit staging configuration
   nano deployment/terraform/vars/staging.tfvars
   
   # Replace these lines:
   prod_project_id = "my-agent-prod-replace-me"
   staging_project_id = "my-agent-staging-replace-me"
   cicd_runner_project_id = "my-agent-cicd-replace-me"
   
   # With your actual project IDs:
   prod_project_id = "your-actual-prod-project"
   staging_project_id = "your-actual-staging-project"
   cicd_runner_project_id = "your-actual-cicd-project"
   ```

2. **For Production Deployment**:
   ```bash
   # Edit production configuration
   nano deployment/terraform/vars/production.tfvars
   
   # Update the same project ID variables
   ```

### Option 2: Update Default Configuration

1. **Edit the default configuration**:
   ```bash
   nano deployment/terraform/vars/env.tfvars
   ```

2. **Replace the placeholder values**:
   ```hcl
   # Replace these:
   prod_project_id = "my-agent-prod-replace-me"
   staging_project_id = "my-agent-staging-replace-me"
   cicd_runner_project_id = "my-agent-cicd-replace-me"
   
   # With your actual project IDs:
   prod_project_id = "your-actual-prod-project"
   staging_project_id = "your-actual-staging-project"
   cicd_runner_project_id = "your-actual-cicd-project"
   ```

### Option 3: Update CI/CD Pipeline Files

1. **Update staging pipeline**:
   ```bash
   nano deployment/cd/staging.yaml
   
   # Replace:
   _STAGING_PROJECT_ID: my-agent-staging-replace-me
   # With:
   _STAGING_PROJECT_ID: your-actual-staging-project
   ```

2. **Update production pipeline**:
   ```bash
   nano deployment/cd/deploy-to-prod.yaml

   # Replace:
   _PROD_PROJECT_ID: my-agent-prod-replace-me
   # With:
   _PROD_PROJECT_ID: your-actual-prod-project
   ```

## 🚀 Deployment Commands

### Using Environment-Specific Files

```bash
# Deploy to staging
cd deployment/terraform
terraform plan --var-file vars/staging.tfvars
terraform apply --var-file vars/staging.tfvars

# Deploy to production
terraform plan --var-file vars/production.tfvars
terraform apply --var-file vars/production.tfvars
```

### Using Default Configuration

```bash
# Deploy using default configuration
cd deployment/terraform
terraform plan --var-file vars/env.tfvars
terraform apply --var-file vars/env.tfvars
```

## ✅ Verification

After updating project IDs, verify the configuration:

```bash
# Check terraform plan
cd deployment/terraform
terraform plan --var-file vars/staging.tfvars

# Should show your actual project IDs instead of "replace-me" values
```

## 🔍 Files That Need Project ID Updates

| File | Purpose | Variables to Update |
|------|---------|-------------------|
| `deployment/terraform/vars/env.tfvars` | Default config | `prod_project_id`, `staging_project_id`, `cicd_runner_project_id` |
| `deployment/terraform/vars/staging.tfvars` | Staging config | `prod_project_id`, `staging_project_id`, `cicd_runner_project_id` |
| `deployment/terraform/vars/production.tfvars` | Production config | `prod_project_id`, `staging_project_id`, `cicd_runner_project_id` |
| `deployment/cd/staging.yaml` | Staging pipeline | `_STAGING_PROJECT_ID` |
| `deployment/cd/deploy-to-prod.yaml` | Production pipeline | `_PROD_PROJECT_ID` |

## 🛡️ Security Notes

- **Never commit real project IDs** to version control if your repository is public
- **Use environment variables** or secure secret management for sensitive project IDs
- **Restrict IAM permissions** to only the necessary roles for each project

## 📞 Support

If you encounter issues:
1. Verify all project IDs are correctly updated
2. Ensure all required APIs are enabled in each project
3. Check IAM permissions for service accounts
4. Review the terraform plan output for any remaining placeholder values
