# =============================================================================
# TERRAFORM VARIABLES CONFIGURATION
# =============================================================================
# This is the default configuration file. For environment-specific deployments,
# use the dedicated files: staging.tfvars or production.tfvars
#
# TODO: Replace the placeholder project IDs below with your actual Google Cloud project IDs

# Project name used for resource naming
project_name = "my-agent"

# =============================================================================
# GOOGLE CLOUD PROJECT IDS
# =============================================================================
# TODO: Replace these placeholder values with your actual Google Cloud project IDs

# Your Production Google Cloud project id
prod_project_id = "my-agent-prod-replace-me"

# Your Staging / Test Google Cloud project id
staging_project_id = "my-agent-staging-replace-me"

# Your Google Cloud project ID that will be used to host the Cloud Build pipelines
cicd_runner_project_id = "my-agent-cicd-replace-me"

# Name of the host connection you created in Cloud Build
host_connection_name = "git-my-agent"

# Name of the repository you added to Cloud Build
repository_name = "repo-my-agent"

# The Google Cloud region you will use to deploy the infrastructure
region = "us-central1"

telemetry_logs_filter = "jsonPayload.attributes.\"traceloop.association.properties.log_type\"=\"tracing\" jsonPayload.resource.attributes.\"service.name\"=\"my-agent\""

feedback_logs_filter = "jsonPayload.log_type=\"feedback\""
