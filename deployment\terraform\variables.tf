# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

variable "project_name" {
  type        = string
  description = "Project name used as a base for resource naming"
  default     = "my-agent"
}

variable "prod_project_id" {
  type        = string
  description = "**Production** Google Cloud Project ID for resource deployment."
}

variable "staging_project_id" {
  type        = string
  description = "**Staging** Google Cloud Project ID for resource deployment."
}

variable "cicd_runner_project_id" {
  type        = string
  description = "Google Cloud Project ID where CI/CD pipelines will execute."
}

variable "region" {
  type        = string
  description = "Google Cloud region for resource deployment."
  default     = "us-central1"
}

variable "host_connection_name" {
  description = "Name of the host connection you created in Cloud Build"
  type        = string
}

variable "repository_name" {
  description = "Name of the repository you'd like to connect to Cloud Build"
  type        = string
}

variable "telemetry_logs_filter" {
  type        = string
  description = "Log Sink filter for capturing telemetry data. Captures logs with the `traceloop.association.properties.log_type` attribute set to `tracing`."
  default     = "jsonPayload.attributes.\"traceloop.association.properties.log_type\"=\"tracing\" jsonPayload.resource.attributes.\"service.name\"=\"my-agent\""
}

variable "feedback_logs_filter" {
  type        = string
  description = "Log Sink filter for capturing feedback data. Captures logs where the `log_type` field is `feedback`."
  default     = "jsonPayload.log_type=\"feedback\""
}


variable "agentengine_sa_roles" {
  description = "List of roles to assign to the Agent Engine service account"

  type        = list(string)
  default = [
    "roles/aiplatform.user",
    "roles/discoveryengine.editor",
    "roles/logging.logWriter",
    "roles/cloudtrace.agent",
    "roles/storage.admin",
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor"
  ]
}

variable "cicd_roles" {
  description = "List of roles to assign to the CICD runner service account in the CICD project"
  type        = list(string)
  default = [
    "roles/storage.admin",
    "roles/aiplatform.user",
    "roles/discoveryengine.editor",
    "roles/logging.logWriter",
    "roles/cloudtrace.agent",
    "roles/artifactregistry.writer",
    "roles/cloudbuild.builds.builder"
  ]
}

variable "cicd_sa_deployment_required_roles" {
  description = "List of roles to assign to the CICD runner service account for the Staging and Prod projects."
  type        = list(string)
  default = [
    "roles/iam.serviceAccountUser",
    "roles/aiplatform.user",
    "roles/storage.admin",
    "roles/cloudsql.client",
    "roles/secretmanager.secretAccessor"
  ]
}

# Database configuration variables
variable "database_tier_staging" {
  type        = string
  description = "Database tier for staging environment"
  default     = "db-f1-micro"
}

variable "database_tier_prod" {
  type        = string
  description = "Database tier for production environment"
  default     = "db-custom-2-4096"
}

variable "database_availability_staging" {
  type        = string
  description = "Database availability type for staging environment"
  default     = "ZONAL"
}

variable "database_availability_prod" {
  type        = string
  description = "Database availability type for production environment"
  default     = "REGIONAL"
}

variable "database_disk_size_staging" {
  type        = number
  description = "Database disk size in GB for staging environment"
  default     = 20
}

variable "database_disk_size_prod" {
  type        = number
  description = "Database disk size in GB for production environment"
  default     = 100
}

variable "database_disk_limit_staging" {
  type        = number
  description = "Database disk auto-resize limit in GB for staging environment"
  default     = 100
}

variable "database_disk_limit_prod" {
  type        = number
  description = "Database disk auto-resize limit in GB for production environment"
  default     = 500
}

variable "database_backup_retention_staging" {
  type        = number
  description = "Database backup retention in days for staging environment"
  default     = 7
}

variable "database_backup_retention_prod" {
  type        = number
  description = "Database backup retention in days for production environment"
  default     = 30
}

variable "database_max_connections_staging" {
  type        = string
  description = "Maximum database connections for staging environment"
  default     = "50"
}

variable "database_max_connections_prod" {
  type        = string
  description = "Maximum database connections for production environment"
  default     = "100"
}


